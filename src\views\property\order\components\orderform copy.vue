<template>
  <div class="task-dialog">
    <el-dialog :before-close="cancelForm" :close-on-click-modal="false" :title="ADDTYPE[type]" :visible.sync="visible"
      append-to-body width="800px">

      <div v-if="showFormData" class="text item task-content">
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="130px">
          <!--额外的表单项-->
          <el-form-item label="计量单位" prop="unit">
            <el-input v-model="ruleForm.unit" placeholder="请输入计量单位" style="width: 550px;" />
          </el-form-item>
          <el-form-item label="入库类型" prop="orderType">
            <el-select v-model="ruleForm.orderType" placeholder="请选择入库类型" style="width: 550px;">
              <el-option label="新设备" value="1" />
              <el-option label="拆回" value="2" />
              <el-option label="维修返回" value="3" />
              <el-option label="后期接收" value="4" />
            </el-select>
          </el-form-item>
          <el-form-item label="数量" prop="amount">
            <el-input v-model="ruleForm.amount" placeholder="请输入数量" style="width: 550px;" />
          </el-form-item>
          <el-form-item label="单价" prop="price">
            <el-input v-model="ruleForm.price" placeholder="请输入单价" style="width: 550px;" />
          </el-form-item>
        </el-form>
        <!-- <fm-generate-form :ref="'generateForm'" :data="formStruct" :preview="viewOrEdit" :remote="remoteFunc"
          :value="formData" /> -->
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="cancelForm">取消</el-button>
        <el-button :disabled="submitDisabled" type="primary" @click="submitAction">
          {{ ADDTYPE[type] }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';
import extendBindTpl from '@/api/system/extendBindTpl';
import { mapGetters } from 'vuex';
import amOrder from '@/api/property/amOrder'
import { convertCurrency } from '@/utils'
const ADDTYPE = {
  1: '创建订单',
  2: '编辑订单'
}
export default {
  components: {},
  data() {
    return {
      visible: false,
      ruleForm: {
        basicData: {},
        enabled: 1,
        unit: '', // 计量单位
        orderType: '' // 入库类型
      },
      rules: {
        unit: [
          { required: true, message: '请输入计量单位', trigger: 'blur' }
        ],
        orderType: [
          { required: true, message: '请选择入库类型', trigger: 'change' }
        ],
        amount: [
          { required: true, message: '请输入数量', trigger: 'blur' }
        ]
      },
      formStruct: {},
      formData: {},
      jsonData: {},
      submitDisabled: false,
      viewOrEdit: false,
      showFormData: false,
      remoteFunc: {
        funcGetToken(resolve) {
          resolve(getToken())
        }
      },
      type: 1,
      ADDTYPE
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  methods: {
    init(info) {
      this.visible = true;
      const { type, id, basicNo } = info;
      this.type = type;
      this.bindId = this.$config.order_key.bindId

      if (type === 1) {
        this.ruleForm.basicData = { basicNo: basicNo }
        this.getProcessNodeList(this.bindId);
      } else {
        this.getContent(id);
      }
    },
    getProcessNodeList(id) {
      this.loading = true;
      const data = { id, enabled: 1 }
      extendBindTpl.get(data).then(res => {
        if (res && res.content && res.content.length) {
          this.processStructureValue = res.content[0];
          this.formStruct = JSON.parse(this.processStructureValue.extendTpl.formStruct);
        }
        this.showFormData = true;
      }).finally(() => {
        this.loading = false;
      });
    },
    getContent(id) {
      amOrder.get({ id, enabled: 1 }).then(res => {
        if (res && res.content) {
          const jsonData = res.content[0];
          this.jsonData = jsonData;
          this.processStructureValue = jsonData;

          // 安全解析JSON的辅助函数
          const safeJsonParse = (jsonString, defaultValue = {}) => {
            try {
              if (!jsonString || jsonString === 'null' || jsonString === 'undefined') {
                return defaultValue;
              }
              return JSON.parse(jsonString);
            } catch (error) {
              console.warn('JSON解析失败:', jsonString, error);
              return defaultValue;
            }
          };

          this.formStruct = safeJsonParse(jsonData.formStruct, {});
          this.formData = safeJsonParse(jsonData.formData, {});
          console.log(this.formStruct);

          // 确保 unitPrice 转换为正确的数字类型
          if (this.formData.unitPrice !== undefined && this.formData.unitPrice !== null) {
            const convertedPrice = convertCurrency(this.formData.unitPrice, false);
            this.formData.unitPrice = Number(convertedPrice);
          }
          this.ruleForm = {
            ...jsonData,
            unit: jsonData.unit || '', // 计量单位
            orderType: jsonData.orderType || '' // 入库类型
          };
          this.showFormData = true;
        }
      })
    },
    submitAction() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          // this.checkModule().then(res => {
          //   if (!res.flag) {
          //     return false;
          //   } else {
          const subData = {
            ...this.ruleForm,
            unit: this.ruleForm.unit, // 确保计量单位被包含
            orderType: this.ruleForm.orderType // 确保入库类型被包含
          };
          let request = amOrder.add;
          let title = '新增';
          if (subData.id) {
            request = amOrder.edit;
            title = '编辑'
          }
          request(subData).then(() => {
            this.$notify({
              title: `${title}成功`,
              type: 'success',
              duration: 2500
            })
            this.cancelForm();
          }).catch((e) => {
            console.log(e);
          })
        }
      })
      // } else {
      //   this.submitDisabled = false
      //   return false
      // }
      // });
    },

    async checkModule() {
      const subData = {
        bindId: this.bindId,
        formData: null,
        formStruct: null,
        formBindToVar: true,
        relation: this.processStructureValue.relation
      };
      if (this.showFormData) {
        return await this.$refs['generateForm'].getData().then(values => {
          console.log(values, '<===>', 'values')
          // 确保 unitPrice 是有效的数字类型
          if (values.unitPrice !== undefined && values.unitPrice !== null) {
            // 如果 unitPrice 已经是数字类型，直接转换为分
            const priceValue = typeof values.unitPrice === 'number' ? values.unitPrice : Number(values.unitPrice);
            if (!isNaN(priceValue)) {
              values.unitPrice = Number(convertCurrency(priceValue, true));
            } else {
              console.error('unitPrice is not a valid number:', values.unitPrice);
              return {
                flag: false
              };
            }
          }
          subData.formData = JSON.stringify(values);
          subData.formStruct = JSON.stringify(this.formStruct);
          return {
            flag: true,
            subData
          };
        }).catch((error) => {
          console.error('Form validation failed:', error);
          return {
            flag: false
          };
        })
      } else {
        return Promise.resolve({
          subData,
          flag: true
        });
      }
    },
    cancelForm() {
      this.visible = false;
      this.$emit('successAction')
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
  display: none;
}
</style>

<style lang="scss" rel="stylesheet/scss">
.no-atTheMoment {
  .el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
    display: none;
  }
}
</style>
